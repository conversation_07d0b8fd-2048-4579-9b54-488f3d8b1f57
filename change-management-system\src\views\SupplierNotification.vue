<template>
  <div class="supplier-notification">
    <div class="page-header">
      <h2>工程变更供应商通知单</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增通知单
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="ECN编号">
          <el-input v-model="searchForm.ecnNumber" placeholder="请输入ECN编号" clearable />
        </el-form-item>
        <el-form-item label="零部件名称">
          <el-input v-model="searchForm.partName" placeholder="请输入零部件名称" clearable />
        </el-form-item>
        <el-form-item label="供应商">
          <el-input v-model="searchForm.supplier" placeholder="请输入供应商" clearable />
        </el-form-item>
        <el-form-item label="通知状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="已发送" value="sent" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="ecnNumber" label="ECN编号" width="120" />
        <el-table-column prop="partName" label="零部件名称" min-width="150" />
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="publishDate" label="发布日期" width="120" />
        <el-table-column prop="notificationDate" label="通知日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleEdit(scope.row)"
                v-if="scope.row.status === 'draft'"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="handleSend(scope.row)"
                v-if="scope.row.status === 'draft'"
              >
                发送
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑供应商通知单' : '新增供应商通知单'"
      width="1200px"
      @close="handleDialogClose"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>变更内容</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="ECN编号" prop="ecnNumber">
                <el-input v-model="formData.ecnNumber" placeholder="请输入ECN编号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发布日期" prop="publishDate">
                <el-date-picker
                  v-model="formData.publishDate"
                  type="date"
                  placeholder="选择发布日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属ECR编号" prop="ecrNumber">
                <el-input v-model="formData.ecrNumber" placeholder="请输入ECR编号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="零部件名称" prop="partName">
                <el-input v-model="formData.partName" placeholder="请输入零部件名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="图号" prop="drawingNumber">
                <el-input v-model="formData.drawingNumber" placeholder="图号" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="新版本" prop="newVersion">
                <el-input v-model="formData.newVersion" placeholder="新版本" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="零件号" prop="partNumber">
                <el-input v-model="formData.partNumber" placeholder="零件号" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="新版本成熟度" prop="maturity">
                <el-input v-model="formData.maturity" placeholder="成熟度" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="变更方案" prop="changeScheme">
                <el-input v-model="formData.changeScheme" placeholder="请输入变更方案" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 库存处理 -->
        <div class="form-section">
          <h3>库存处理</h3>
          <el-table :data="formData.inventoryItems" style="width: 100%" border>
            <el-table-column label="零部件名称" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.partName" placeholder="零部件名称" />
              </template>
            </el-table-column>
            <el-table-column label="零件号" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.partNumber" placeholder="零件号" />
              </template>
            </el-table-column>
            <el-table-column label="旧版本" width="100">
              <template #default="scope">
                <el-input v-model="scope.row.oldVersion" placeholder="旧版本" />
              </template>
            </el-table-column>
            <el-table-column label="供应商" width="240">
              <el-table-column label="在订处置" width="80">
                <template #default="scope">
                  <el-input v-model="scope.row.supplier.ordered" placeholder="处置" />
                </template>
              </el-table-column>
              <el-table-column label="在制处置" width="80">
                <template #default="scope">
                  <el-input v-model="scope.row.supplier.manufacturing" placeholder="处置" />
                </template>
              </el-table-column>
              <el-table-column label="库存处置" width="80">
                <template #default="scope">
                  <el-input v-model="scope.row.supplier.inventory" placeholder="处置" />
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="备注" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.remarks" placeholder="备注" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeInventoryItem(scope.$index)"
                  v-if="formData.inventoryItems.length > 1"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" @click="addInventoryItem" style="margin-top: 10px">添加库存项</el-button>
        </div>

        <!-- 备注 -->
        <div class="form-section">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>

        <!-- 旧订单处理 -->
        <div class="form-section">
          <h3>旧订单处理</h3>
          <el-table :data="formData.oldOrders" style="width: 100%" border>
            <el-table-column prop="orderNumber" label="旧订单号" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.orderNumber" placeholder="订单号" />
              </template>
            </el-table-column>
            <el-table-column prop="partName" label="零部件名称" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.partName" placeholder="零部件名称" />
              </template>
            </el-table-column>
            <el-table-column prop="partNumber" label="零件号" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.partNumber" placeholder="零件号" />
              </template>
            </el-table-column>
            <el-table-column prop="oldVersion" label="旧版本" width="100">
              <template #default="scope">
                <el-input v-model="scope.row.oldVersion" placeholder="旧版本" />
              </template>
            </el-table-column>
            <el-table-column prop="handling" label="处置" width="100">
              <template #default="scope">
                <el-select v-model="scope.row.handling" placeholder="选择处置方式">
                  <el-option label="作废" value="cancel" />
                  <el-option label="继续" value="continue" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="newOrderNumber" label="替代新订单" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.newOrderNumber" placeholder="新订单号" />
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.remarks" placeholder="备注" />
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" @click="addOldOrder" style="margin-top: 10px">添加订单</el-button>
        </div>

        <!-- 变更信息收到确认 -->
        <div class="form-section">
          <h3>变更信息收到确认（收到后签字并提交至第五部门时计划完成时间，于两天内反馈采购）</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="接收人">
                <el-input v-model="formData.confirmation.receiver" placeholder="接收人" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="职务">
                <el-input v-model="formData.confirmation.position" placeholder="职务" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="电话">
                <el-input v-model="formData.confirmation.phone" placeholder="电话" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="邮箱">
                <el-input v-model="formData.confirmation.email" placeholder="邮箱" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="接收人签字">
                <el-input v-model="formData.confirmation.signature" placeholder="接收人签字" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 供应商变更信息反馈 -->
        <div class="form-section">
          <h3>供应商变更信息反馈（由供应商填写，完成后发次反馈SQE）</h3>
          <el-table :data="formData.supplierFeedback" style="width: 100%" border>
            <el-table-column prop="item" label="项目" width="300" />
            <el-table-column prop="result" label="Y/N/NA" width="100">
              <template #default="scope">
                <el-select v-model="scope.row.result" placeholder="选择">
                  <el-option label="Y" value="Y" />
                  <el-option label="N" value="N" />
                  <el-option label="NA" value="NA" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="responsible" label="责任人" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.responsible" placeholder="责任人" />
              </template>
            </el-table-column>
            <el-table-column prop="plannedTime" label="计划完成时间" width="150">
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.plannedTime"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column prop="actualTime" label="实际完成时间" width="150">
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.actualTime"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column prop="evidence" label="提交的证据资料" min-width="200">
              <template #default="scope">
                <el-input v-model="scope.row.evidence" placeholder="证据资料" />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- SQE确认 -->
        <div class="form-section">
          <h3>SQE确认（供应商变更完成状态）</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="新版本零件首批出次及数量">
                <el-input v-model="formData.sqeConfirmation.firstBatch" placeholder="首批出次及数量" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查确认">
                <el-input v-model="formData.sqeConfirmation.inspection" placeholder="检查确认" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="SQE确认支更完成（签名/日期）">
                <el-input v-model="formData.sqeConfirmation.confirmation" placeholder="SQE确认签名" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存草稿</el-button>
          <el-button type="success" @click="handleSubmit">发送通知</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  ecnNumber: '',
  partName: '',
  supplier: '',
  status: ''
})

// 表单数据
const formData = reactive({
  ecnNumber: '',
  publishDate: '',
  ecrNumber: '',
  partName: '',
  drawingNumber: '',
  newVersion: '',
  partNumber: '',
  maturity: '',
  changeScheme: '',
  inventoryItems: [
    {
      partName: '',
      partNumber: '',
      oldVersion: '',
      supplier: {
        ordered: '',
        manufacturing: '',
        inventory: ''
      },
      remarks: ''
    }
  ],
  remarks: '',
  oldOrders: [
    {
      orderNumber: '',
      partName: '',
      partNumber: '',
      oldVersion: '',
      handling: '',
      newOrderNumber: '',
      remarks: ''
    }
  ],
  confirmation: {
    receiver: '',
    position: '',
    phone: '',
    email: '',
    signature: ''
  },
  supplierFeedback: [
    { item: '是否完成旧物料处理（库存/在制品等）', result: '', responsible: '', plannedTime: '', actualTime: '', evidence: '' },
    { item: '是否完成文件变更和旧版本文件回收', result: '', responsible: '', plannedTime: '', actualTime: '', evidence: '' },
    { item: '是否完成生产准备（设备/工装/模具等）', result: '', responsible: '', plannedTime: '', actualTime: '', evidence: '' },
    { item: '是否完成新品生产（新点）', result: '', responsible: '', plannedTime: '', actualTime: '', evidence: '' },
    { item: '有无其他注意事项/备注', result: '', responsible: '', plannedTime: '', actualTime: '', evidence: '' }
  ],
  sqeConfirmation: {
    firstBatch: '',
    inspection: '',
    confirmation: ''
  }
})

// 表单验证规则
const formRules = {
  ecnNumber: [{ required: true, message: '请输入ECN编号', trigger: 'blur' }],
  publishDate: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  partName: [{ required: true, message: '请输入零部件名称', trigger: 'blur' }]
}

// 示例数据
const tableData = ref([
  {
    ecnNumber: 'ECN-2024-001',
    partName: '发动机缸体',
    supplier: '供应商A',
    publishDate: '2024-01-15',
    notificationDate: '2024-01-16',
    status: 'sent'
  },
  {
    ecnNumber: 'ECN-2024-002',
    partName: '变速箱齿轮',
    supplier: '供应商B',
    publishDate: '2024-01-14',
    notificationDate: '2024-01-15',
    status: 'confirmed'
  },
  {
    ecnNumber: 'ECN-2024-003',
    partName: '制动卡钳',
    supplier: '供应商C',
    publishDate: '2024-01-13',
    notificationDate: '2024-01-14',
    status: 'completed'
  },
  {
    ecnNumber: 'ECN-2024-004',
    partName: '车身焊接件',
    supplier: '供应商D',
    publishDate: '2024-01-12',
    notificationDate: '',
    status: 'draft'
  },
  {
    ecnNumber: 'ECN-2024-005',
    partName: '悬架减震器',
    supplier: '供应商E',
    publishDate: '2024-01-11',
    notificationDate: '2024-01-12',
    status: 'sent'
  }
])

// 工具函数
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    sent: '已发送',
    confirmed: '已确认',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    sent: 'warning',
    confirmed: 'primary',
    completed: 'success'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    ecnNumber: '',
    partName: '',
    supplier: '',
    status: ''
  })
}

const handleView = (row: any) => {
  console.log('查看', row)
  ElMessage.info('查看功能待实现')
}

const handleEdit = (row: any) => {
  isEdit.value = true
  showAddDialog.value = true
  // 这里应该加载具体的数据
}

const handleSend = (row: any) => {
  ElMessageBox.confirm('确定要发送这个通知单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = 'sent'
    row.notificationDate = new Date().toISOString().split('T')[0]
    ElMessage.success('发送成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleDialogClose = () => {
  isEdit.value = false
  // 重置表单数据
  formRef.value?.resetFields()
}

const addInventoryItem = () => {
  formData.inventoryItems.push({
    partName: '',
    partNumber: '',
    oldVersion: '',
    supplier: {
      ordered: '',
      manufacturing: '',
      inventory: ''
    },
    remarks: ''
  })
}

const removeInventoryItem = (index: number) => {
  formData.inventoryItems.splice(index, 1)
}

const addOldOrder = () => {
  formData.oldOrders.push({
    orderNumber: '',
    partName: '',
    partNumber: '',
    oldVersion: '',
    handling: '',
    newOrderNumber: '',
    remarks: ''
  })
}

const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存草稿成功')
      showAddDialog.value = false
    }
  })
}

const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('发送通知成功')
      showAddDialog.value = false
    }
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.supplier-notification {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
