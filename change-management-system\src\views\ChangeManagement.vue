<template>
  <div class="change-management">
    <div class="page-header">
      <h2>变更管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增变更申请
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更标题">
          <el-input v-model="searchForm.title" placeholder="请输入变更标题" clearable />
        </el-form-item>
        <el-form-item label="变更类型">
          <el-select v-model="searchForm.type" placeholder="请选择变更类型" clearable>
            <el-option label="工艺变更" value="process" />
            <el-option label="设备变更" value="equipment" />
            <el-option label="原材料变更" value="material" />
            <el-option label="检验方法变更" value="inspection" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="待审批" value="pending" />
            <el-option label="审批中" value="reviewing" />
            <el-option label="已驳回" value="rejected" />
            <el-option label="待执行" value="approved" />
            <el-option label="执行中" value="executing" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="变更编号" width="120" />
        <el-table-column prop="title" label="变更标题" min-width="200" />
        <el-table-column prop="type" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="变更等级" width="100">
          <template #default="scope">
            <el-tag :type="getLevelTagType(scope.row.level)">
              {{ getLevelLabel(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100" />
        <el-table-column prop="applyDate" label="申请日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleEdit(scope.row)"
                v-if="scope.row.status === 'draft' || scope.row.status === 'rejected'"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="handleDelete(scope.row)"
                v-if="scope.row.status === 'draft'"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑工程变更申请' : '新增工程变更申请'"
      width="1200px"
      @close="handleDialogClose"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="ECN编号" prop="ecnNumber">
                <el-input v-model="formData.ecnNumber" placeholder="自动生成" disabled style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发布日期" prop="publishDate">
                <el-date-picker
                  v-model="formData.publishDate"
                  type="date"
                  placeholder="选择发布日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属ECR编号" prop="ecrNumber">
                <el-input v-model="formData.ecrNumber" placeholder="请输入ECR编号" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="项目编号" prop="projectNumber">
                <el-input v-model="formData.projectNumber" placeholder="请输入项目编号" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="formData.projectName" placeholder="请输入项目名称" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉及总成" prop="involvedAssembly">
                <el-input v-model="formData.involvedAssembly" placeholder="请输入涉及总成" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="成熟度" prop="maturity">
                <el-input v-model="formData.maturity" placeholder="请输入成熟度" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 变更内容 -->
        <div class="form-section">
          <h3>变更内容</h3>
          <div class="change-items">
            <div v-for="(item, index) in formData.changeItems" :key="index" class="change-item">
              <h4>{{ index + 1 }}. 变更原因</h4>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="零部件名称">
                    <el-input v-model="item.partName" placeholder="零部件名称" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="图号">
                    <el-input v-model="item.drawingNumber" placeholder="图号" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="新版本">
                    <el-input v-model="item.newVersion" placeholder="新版本" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="零件号">
                    <el-input v-model="item.partNumber" placeholder="零件号" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="新版本成熟度">
                    <el-input v-model="item.newVersionMaturity" placeholder="成熟度" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="变更方案">
                    <el-input
                      v-model="item.changeScheme"
                      type="textarea"
                      :rows="3"
                      placeholder="请详细描述变更方案"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button
                type="danger"
                size="small"
                @click="removeChangeItem(index)"
                v-if="formData.changeItems.length > 1"
              >
                删除此项
              </el-button>
            </div>
            <el-button type="primary" @click="addChangeItem">添加变更项</el-button>
          </div>
        </div>

        <!-- 库存处理方案 -->
        <div class="form-section">
          <h3>库存处理方案</h3>
          <el-table :data="formData.inventoryItems" style="width: 100%">
            <el-table-column label="零部件名称" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.partName" placeholder="零部件名称" />
              </template>
            </el-table-column>
            <el-table-column label="零件号" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.partNumber" placeholder="零件号" />
              </template>
            </el-table-column>
            <el-table-column label="旧版本" width="100">
              <template #default="scope">
                <el-input v-model="scope.row.oldVersion" placeholder="旧版本" />
              </template>
            </el-table-column>
            <el-table-column label="供应商" width="360">
              <template #header>
                <span>供应商</span>
              </template>
              <el-table-column label="在订处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.supplier.ordered.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.supplier.ordered.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="在制处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.supplier.manufacturing.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.supplier.manufacturing.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="库存处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.supplier.inventory.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.supplier.inventory.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="内部" width="360">
              <el-table-column label="库存处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.internal.inventory.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.internal.inventory.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="在制处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.internal.manufacturing.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.internal.manufacturing.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="总成处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.internal.assembly.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.internal.assembly.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="客户" width="120">
              <el-table-column label="总成处置" width="120">
                <template #default="scope">
                  <div style="display: flex; gap: 5px;">
                    <el-input v-model="scope.row.customer.assembly.quantity" placeholder="数量" style="width: 60px;" />
                    <el-select v-model="scope.row.customer.assembly.method" placeholder="处理方式" style="width: 55px;">
                      <el-option label="作废" value="cancel" />
                      <el-option label="继续" value="continue" />
                      <el-option label="返工" value="rework" />
                      <el-option label="报废" value="scrap" />
                    </el-select>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeInventoryItem(scope.$index)"
                  v-if="formData.inventoryItems.length > 1"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" @click="addInventoryItem" style="margin-top: 10px">添加库存项</el-button>
        </div>

        <!-- 备注 -->
        <div class="form-section">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存草稿</el-button>
          <el-button type="success" @click="handleSubmit">提交审批</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  status: ''
})

// 表单数据
const formData = reactive({
  id: '',
  ecnNumber: '',
  publishDate: '',
  ecrNumber: '',
  projectNumber: '',
  projectName: '',
  involvedAssembly: '',
  maturity: '',
  changeItems: [
    {
      partName: '',
      drawingNumber: '',
      newVersion: '',
      partNumber: '',
      newVersionMaturity: '',
      changeScheme: ''
    }
  ],
  inventoryItems: [
    {
      partName: '',
      partNumber: '',
      oldVersion: '',
      supplier: {
        ordered: { quantity: '', method: '' },
        manufacturing: { quantity: '', method: '' },
        inventory: { quantity: '', method: '' }
      },
      internal: {
        inventory: { quantity: '', method: '' },
        manufacturing: { quantity: '', method: '' },
        assembly: { quantity: '', method: '' }
      },
      customer: {
        assembly: { quantity: '', method: '' }
      }
    }
  ],
  remarks: ''
})

// 表单验证规则
const formRules = {
  publishDate: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  projectNumber: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  involvedAssembly: [{ required: true, message: '请输入涉及总成', trigger: 'blur' }]
}

// 示例数据
const tableData = ref([
  {
    id: 'ECN-2024-001',
    title: '发动机缸体工艺变更',
    type: 'process',
    level: 'normal',
    applicant: '张工程师',
    applyDate: '2024-01-15',
    status: 'reviewing',
    ecnNumber: 'ECN-2024-001',
    projectNumber: 'PRJ-001',
    projectName: '新能源发动机项目',
    involvedAssembly: '发动机总成'
  },
  {
    id: 'ECN-2024-002',
    title: '变速箱齿轮材料变更',
    type: 'material',
    level: 'major',
    applicant: '李主管',
    applyDate: '2024-01-14',
    status: 'pending',
    ecnNumber: 'ECN-2024-002',
    projectNumber: 'PRJ-002',
    projectName: '8AT变速箱项目',
    involvedAssembly: '变速箱总成'
  },
  {
    id: 'ECN-2024-003',
    title: '制动系统卡钳设计变更',
    type: 'design',
    level: 'minor',
    applicant: '王工程师',
    applyDate: '2024-01-13',
    status: 'approved',
    ecnNumber: 'ECN-2024-003',
    projectNumber: 'PRJ-003',
    projectName: '制动系统升级项目',
    involvedAssembly: '制动系统'
  },
  {
    id: 'ECN-2024-004',
    title: '车身焊接工艺优化',
    type: 'process',
    level: 'normal',
    applicant: '赵技师',
    applyDate: '2024-01-12',
    status: 'completed',
    ecnNumber: 'ECN-2024-004',
    projectNumber: 'PRJ-004',
    projectName: '车身轻量化项目',
    involvedAssembly: '车身总成'
  },
  {
    id: 'ECN-2024-005',
    title: '悬架系统减震器规格变更',
    type: 'specification',
    level: 'major',
    applicant: '陈经理',
    applyDate: '2024-01-11',
    status: 'draft',
    ecnNumber: 'ECN-2024-005',
    projectNumber: 'PRJ-005',
    projectName: '底盘系统优化项目',
    involvedAssembly: '悬架系统'
  }
])

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    process: '工艺变更',
    equipment: '设备变更',
    material: '原材料变更',
    inspection: '检验方法变更'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    process: 'primary',
    equipment: 'success',
    material: 'warning',
    inspection: 'info'
  }
  return types[type] || ''
}

const getLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    major: '重大变更',
    normal: '一般变更',
    minor: '轻微变更'
  }
  return labels[level] || level
}

const getLevelTagType = (level: string) => {
  const types: Record<string, string> = {
    major: 'danger',
    normal: 'warning',
    minor: 'success'
  }
  return types[level] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    pending: '待审批',
    reviewing: '审批中',
    rejected: '已驳回',
    approved: '待执行',
    executing: '执行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    reviewing: 'primary',
    rejected: 'danger',
    approved: 'success',
    executing: 'primary',
    completed: 'success'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
  // 这里应该调用API进行搜索
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    type: '',
    status: ''
  })
}

const handleView = (row: any) => {
  console.log('查看', row)
  ElMessage.info('查看功能待实现')
}

const handleEdit = (row: any) => {
  isEdit.value = true
  showAddDialog.value = true
  Object.assign(formData, row)
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条变更申请吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleDialogClose = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: '',
    ecnNumber: '',
    publishDate: '',
    ecrNumber: '',
    projectNumber: '',
    projectName: '',
    involvedAssembly: '',
    maturity: '',
    changeItems: [
      {
        partName: '',
        drawingNumber: '',
        newVersion: '',
        partNumber: '',
        newVersionMaturity: '',
        changeScheme: ''
      }
    ],
    inventoryItems: [
      {
        partName: '',
        partNumber: '',
        oldVersion: '',
        supplier: {
          ordered: { quantity: '', method: '' },
          manufacturing: { quantity: '', method: '' },
          inventory: { quantity: '', method: '' }
        },
        internal: {
          inventory: { quantity: '', method: '' },
          manufacturing: { quantity: '', method: '' },
          assembly: { quantity: '', method: '' }
        },
        customer: {
          assembly: { quantity: '', method: '' }
        }
      }
    ],
    remarks: ''
  })
  formRef.value?.resetFields()
}

// 添加变更项
const addChangeItem = () => {
  formData.changeItems.push({
    partName: '',
    drawingNumber: '',
    newVersion: '',
    partNumber: '',
    newVersionMaturity: '',
    changeScheme: ''
  })
}

// 删除变更项
const removeChangeItem = (index: number) => {
  formData.changeItems.splice(index, 1)
}

// 添加库存项
const addInventoryItem = () => {
  formData.inventoryItems.push({
    partName: '',
    partNumber: '',
    oldVersion: '',
    supplier: {
      ordered: { quantity: '', method: '' },
      manufacturing: { quantity: '', method: '' },
      inventory: { quantity: '', method: '' }
    },
    internal: {
      inventory: { quantity: '', method: '' },
      manufacturing: { quantity: '', method: '' },
      assembly: { quantity: '', method: '' }
    },
    customer: {
      assembly: { quantity: '', method: '' }
    }
  })
}

// 删除库存项
const removeInventoryItem = (index: number) => {
  formData.inventoryItems.splice(index, 1)
}

const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存草稿成功')
      showAddDialog.value = false
    }
  })
}

const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('提交审批成功')
      showAddDialog.value = false
    }
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.change-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.form-section h4 {
  margin: 15px 0 10px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.change-items {
  background-color: white;
  padding: 15px;
  border-radius: 4px;
}

.change-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f9f9f9;
}
</style>
