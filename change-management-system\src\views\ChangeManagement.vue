<template>
  <div class="change-management">
    <div class="page-header">
      <h2>变更管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增变更申请
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更标题">
          <el-input v-model="searchForm.title" placeholder="请输入变更标题" clearable />
        </el-form-item>
        <el-form-item label="变更类型">
          <el-select v-model="searchForm.type" placeholder="请选择变更类型" clearable>
            <el-option label="工艺变更" value="process" />
            <el-option label="设备变更" value="equipment" />
            <el-option label="原材料变更" value="material" />
            <el-option label="检验方法变更" value="inspection" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="待审批" value="pending" />
            <el-option label="审批中" value="reviewing" />
            <el-option label="已驳回" value="rejected" />
            <el-option label="待执行" value="approved" />
            <el-option label="执行中" value="executing" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="变更编号" width="120" />
        <el-table-column prop="title" label="变更标题" min-width="200" />
        <el-table-column prop="type" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="变更等级" width="100">
          <template #default="scope">
            <el-tag :type="getLevelTagType(scope.row.level)">
              {{ getLevelLabel(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100" />
        <el-table-column prop="applyDate" label="申请日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleEdit(scope.row)"
                v-if="scope.row.status === 'draft' || scope.row.status === 'rejected'"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="handleDelete(scope.row)"
                v-if="scope.row.status === 'draft'"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑变更申请' : '新增变更申请'"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="变更标题" prop="title">
              <el-input v-model="formData.title" placeholder="请输入变更标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="formData.applicant" placeholder="请输入申请人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="变更类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择变更类型">
                <el-option label="工艺变更" value="process" />
                <el-option label="设备变更" value="equipment" />
                <el-option label="原材料变更" value="material" />
                <el-option label="检验方法变更" value="inspection" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变更等级" prop="level">
              <el-select v-model="formData.level" placeholder="请选择变更等级">
                <el-option label="重大变更" value="major" />
                <el-option label="一般变更" value="normal" />
                <el-option label="轻微变更" value="minor" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="变更前说明" prop="beforeDescription">
          <el-input
            v-model="formData.beforeDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细描述变更实施前的过程状态、存在的问题或改进点"
          />
        </el-form-item>
        <el-form-item label="变更后说明" prop="afterDescription">
          <el-input
            v-model="formData.afterDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细描述变更实施后预期的过程状态、预期效果、可能带来的影响"
          />
        </el-form-item>
        <el-form-item label="影响评估" prop="impactAssessment">
          <el-input
            v-model="formData.impactAssessment"
            type="textarea"
            :rows="3"
            placeholder="请评估变更可能对产品质量、生产效率、成本、安全、环境等方面的影响"
          />
        </el-form-item>
        <el-form-item label="审批流程" prop="approvalFlow">
          <el-select v-model="formData.approvalFlow" placeholder="请选择审批流程">
            <el-option label="轻微变更流程" value="minor_flow" />
            <el-option label="一般变更流程" value="normal_flow" />
            <el-option label="重大变更流程" value="major_flow" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存草稿</el-button>
          <el-button type="success" @click="handleSubmit">提交审批</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  status: ''
})

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  applicant: '',
  type: '',
  level: '',
  beforeDescription: '',
  afterDescription: '',
  impactAssessment: '',
  approvalFlow: ''
})

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入变更标题', trigger: 'blur' }],
  applicant: [{ required: true, message: '请输入申请人', trigger: 'blur' }],
  type: [{ required: true, message: '请选择变更类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择变更等级', trigger: 'change' }],
  beforeDescription: [{ required: true, message: '请输入变更前说明', trigger: 'blur' }],
  afterDescription: [{ required: true, message: '请输入变更后说明', trigger: 'blur' }],
  impactAssessment: [{ required: true, message: '请输入影响评估', trigger: 'blur' }],
  approvalFlow: [{ required: true, message: '请选择审批流程', trigger: 'change' }]
}

// 示例数据
const tableData = ref([
  {
    id: 'CHG-2024-001',
    title: '发动机装配工艺优化',
    type: 'process',
    level: 'normal',
    applicant: '张工程师',
    applyDate: '2024-01-15',
    status: 'reviewing'
  },
  {
    id: 'CHG-2024-002',
    title: '焊接设备升级改造',
    type: 'equipment',
    level: 'major',
    applicant: '李主管',
    applyDate: '2024-01-14',
    status: 'pending'
  },
  {
    id: 'CHG-2024-003',
    title: '钢材供应商变更',
    type: 'material',
    level: 'minor',
    applicant: '王采购',
    applyDate: '2024-01-13',
    status: 'approved'
  },
  {
    id: 'CHG-2024-004',
    title: '质检标准更新',
    type: 'inspection',
    level: 'normal',
    applicant: '赵质检',
    applyDate: '2024-01-12',
    status: 'completed'
  },
  {
    id: 'CHG-2024-005',
    title: '生产线布局调整',
    type: 'process',
    level: 'major',
    applicant: '陈经理',
    applyDate: '2024-01-11',
    status: 'draft'
  }
])

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    process: '工艺变更',
    equipment: '设备变更',
    material: '原材料变更',
    inspection: '检验方法变更'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    process: 'primary',
    equipment: 'success',
    material: 'warning',
    inspection: 'info'
  }
  return types[type] || ''
}

const getLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    major: '重大变更',
    normal: '一般变更',
    minor: '轻微变更'
  }
  return labels[level] || level
}

const getLevelTagType = (level: string) => {
  const types: Record<string, string> = {
    major: 'danger',
    normal: 'warning',
    minor: 'success'
  }
  return types[level] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    pending: '待审批',
    reviewing: '审批中',
    rejected: '已驳回',
    approved: '待执行',
    executing: '执行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    reviewing: 'primary',
    rejected: 'danger',
    approved: 'success',
    executing: 'primary',
    completed: 'success'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
  // 这里应该调用API进行搜索
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    type: '',
    status: ''
  })
}

const handleView = (row: any) => {
  console.log('查看', row)
  ElMessage.info('查看功能待实现')
}

const handleEdit = (row: any) => {
  isEdit.value = true
  showAddDialog.value = true
  Object.assign(formData, row)
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条变更申请吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleDialogClose = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: '',
    title: '',
    applicant: '',
    type: '',
    level: '',
    beforeDescription: '',
    afterDescription: '',
    impactAssessment: '',
    approvalFlow: ''
  })
  formRef.value?.resetFields()
}

const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存草稿成功')
      showAddDialog.value = false
    }
  })
}

const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('提交审批成功')
      showAddDialog.value = false
    }
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.change-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
