import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/change-management',
    },
    {
      path: '/change-management',
      name: 'change-management',
      component: () => import('../views/ChangeManagement.vue'),
    },
    {
      path: '/change-review',
      name: 'change-review',
      component: () => import('../views/ChangeReview.vue'),
    },
    {
      path: '/change-execution',
      name: 'change-execution',
      component: () => import('../views/ChangeExecution.vue'),
    },
    {
      path: '/supplier-notification',
      name: 'supplier-notification',
      component: () => import('../views/SupplierNotification.vue'),
    },
    {
      path: '/system-config',
      name: 'system-config',
      component: () => import('../views/SystemConfig.vue'),
    },
  ],
})

export default router
