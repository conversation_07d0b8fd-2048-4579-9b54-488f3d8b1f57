<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref } from 'vue'

const isCollapse = ref(false)

const handleMenuCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<template>
  <div class="app-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo-container">
          <h3 v-if="!isCollapse">变更管理系统</h3>
          <h3 v-else>变更</h3>
        </div>
        <el-menu
          default-active="1"
          class="el-menu-vertical"
          :collapse="isCollapse"
          router
        >
          <el-menu-item index="/change-management">
            <el-icon><Document /></el-icon>
            <template #title>变更管理</template>
          </el-menu-item>
          <el-menu-item index="/change-review">
            <el-icon><Check /></el-icon>
            <template #title>变更审核</template>
          </el-menu-item>
          <el-menu-item index="/change-execution">
            <el-icon><Setting /></el-icon>
            <template #title>变更执行</template>
          </el-menu-item>
          <el-menu-item index="/supplier-notification">
            <el-icon><Bell /></el-icon>
            <template #title>供应商通知单</template>
          </el-menu-item>
          <el-menu-item index="/system-config">
            <el-icon><Tools /></el-icon>
            <template #title>系统配置</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button @click="handleMenuCollapse" :icon="isCollapse ? 'Expand' : 'Fold'" />
            <span class="title">汽车制造工厂过程变更管理系统</span>
          </div>
          <div class="header-right">
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-icon><User /></el-icon>
                管理员
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主要内容 -->
        <el-main class="main-content">
          <RouterView />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
}

.logo-container h3 {
  margin: 0;
  font-size: 16px;
}

.el-menu-vertical {
  border-right: none;
  background-color: #304156;
}

.el-menu-vertical .el-menu-item {
  color: #bfcbd9;
}

.el-menu-vertical .el-menu-item:hover {
  background-color: #263445;
  color: #409eff;
}

.el-menu-vertical .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}
</style>
