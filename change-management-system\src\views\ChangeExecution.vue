<template>
  <div class="change-execution">
    <div class="page-header">
      <h2>变更执行</h2>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更编号">
          <el-input v-model="searchForm.id" placeholder="请输入变更编号" clearable />
        </el-form-item>
        <el-form-item label="变更标题">
          <el-input v-model="searchForm.title" placeholder="请输入变更标题" clearable />
        </el-form-item>
        <el-form-item label="执行状态">
          <el-select v-model="searchForm.status" placeholder="请选择执行状态" clearable>
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="executing" />
            <el-option label="待验证" value="verifying" />
            <el-option label="已完成" value="completed" />
            <el-option label="已延期" value="delayed" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任人">
          <el-input v-model="searchForm.executor" placeholder="请输入责任人" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="变更编号" width="120" />
        <el-table-column prop="title" label="变更标题" min-width="200" />
        <el-table-column prop="type" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executor" label="责任人" width="100" />
        <el-table-column prop="planStartDate" label="计划开始" width="120" />
        <el-table-column prop="planEndDate" label="计划完成" width="120" />
        <el-table-column prop="actualProgress" label="执行进度" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.actualProgress" :status="getProgressStatus(scope.row.actualProgress)" />
          </template>
        </el-table-column>
        <el-table-column prop="executionStatus" label="执行状态" width="100">
          <template #default="scope">
            <el-tag :type="getExecutionStatusTagType(scope.row.executionStatus)">
              {{ getExecutionStatusLabel(scope.row.executionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleExecute(scope.row)"
                v-if="scope.row.executionStatus === 'pending' || scope.row.executionStatus === 'executing'"
              >
                执行管理
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="handleVerify(scope.row)"
                v-if="scope.row.executionStatus === 'verifying'"
              >
                效果验证
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 执行管理对话框 -->
    <el-dialog
      v-model="showExecuteDialog"
      title="工程变更执行计划跟踪"
      width="1200px"
      @close="handleExecuteDialogClose"
    >
      <div class="execute-content">
        <!-- 变更内容 -->
        <div class="form-section">
          <h3>变更内容</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ECN编号">{{ currentExecuteItem.id }}</el-descriptions-item>
            <el-descriptions-item label="发布日期">{{ currentExecuteItem.publishDate }}</el-descriptions-item>
            <el-descriptions-item label="所属ECR编号">{{ currentExecuteItem.ecrNumber }}</el-descriptions-item>
            <el-descriptions-item label="项目编号">{{ currentExecuteItem.projectNumber }}</el-descriptions-item>
            <el-descriptions-item label="项目名称">{{ currentExecuteItem.title }}</el-descriptions-item>
            <el-descriptions-item label="涉及总成">{{ currentExecuteItem.involvedAssembly }}</el-descriptions-item>
            <el-descriptions-item label="成熟度">{{ currentExecuteItem.maturity }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 变更执行检查表 -->
        <div class="form-section">
          <h3>变更执行检查表</h3>
          <el-table :data="currentExecuteItem.executionChecklist" style="width: 100%" border>
            <el-table-column prop="department" label="部门" width="120" />
            <el-table-column prop="executionStatus" label="执行情况" min-width="300" />
            <el-table-column prop="plannedTime" label="计划完成时间" width="150" />
            <el-table-column prop="actualTime" label="实际完成时间" width="150" />
            <el-table-column prop="attachments" label="各部门执行计划文件附件" width="200" />
          </el-table>
        </div>

        <!-- 工程变更效果验证 -->
        <div class="form-section">
          <h3>工程变更效果验证</h3>
          <el-table :data="currentExecuteItem.effectVerification" style="width: 100%" border>
            <el-table-column prop="sequence" label="序号" width="80" />
            <el-table-column prop="verificationItem" label="确认项" min-width="300" />
            <el-table-column prop="result" label="Y/N/NA" width="100">
              <template #default="scope">
                <el-select v-model="scope.row.result" placeholder="选择">
                  <el-option label="Y" value="Y" />
                  <el-option label="N" value="N" />
                  <el-option label="NA" value="NA" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" min-width="200">
              <template #default="scope">
                <el-input v-model="scope.row.remarks" placeholder="请输入备注" />
              </template>
            </el-table-column>
          </el-table>

          <div style="margin-top: 20px;">
            <el-form label-width="150px">
              <el-form-item label="其他注意事项/备注">
                <el-input
                  v-model="currentExecuteItem.otherRemarks"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入其他注意事项或备注"
                />
              </el-form-item>
              <el-form-item label="CCB确认签章">
                <el-input
                  v-model="currentExecuteItem.ccbConfirmation"
                  placeholder="CCB确认签章"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 执行记录 -->
        <div class="form-section">
          <h3>执行记录</h3>
          <el-form :model="executeForm" label-width="120px">
            <el-form-item label="执行日期">
              <el-date-picker
                v-model="executeForm.executeDate"
                type="date"
                placeholder="选择执行日期"
              />
            </el-form-item>
            <el-form-item label="执行内容">
              <el-input
                v-model="executeForm.executeContent"
                type="textarea"
                :rows="3"
                placeholder="请描述具体执行内容"
              />
            </el-form-item>
            <el-form-item label="执行结果">
              <el-input
                v-model="executeForm.executeResult"
                type="textarea"
                :rows="3"
                placeholder="请描述执行结果"
              />
            </el-form-item>
            <el-form-item label="风险点">
              <el-input
                v-model="executeForm.riskPoints"
                type="textarea"
                :rows="2"
                placeholder="请描述执行过程中的风险点"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExecuteDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveExecuteRecord">保存记录</el-button>
          <el-button type="success" @click="handleCompleteExecution">完成执行</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 效果验证对话框 -->
    <el-dialog
      v-model="showVerifyDialog"
      title="效果验证"
      width="700px"
    >
      <div class="verify-content">
        <h3>变更信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更编号">{{ currentVerifyItem.id }}</el-descriptions-item>
          <el-descriptions-item label="变更标题">{{ currentVerifyItem.title }}</el-descriptions-item>
          <el-descriptions-item label="责任人">{{ currentVerifyItem.executor }}</el-descriptions-item>
          <el-descriptions-item label="完成日期">{{ currentVerifyItem.actualEndDate }}</el-descriptions-item>
        </el-descriptions>

        <h3>验证表单</h3>
        <el-form :model="verifyForm" label-width="120px">
          <el-form-item label="验证日期">
            <el-date-picker
              v-model="verifyForm.verifyDate"
              type="date"
              placeholder="选择验证日期"
            />
          </el-form-item>
          <el-form-item label="验证人员">
            <el-input v-model="verifyForm.verifier" placeholder="请输入验证人员" />
          </el-form-item>
          <el-form-item label="验证结果">
            <el-radio-group v-model="verifyForm.verifyResult">
              <el-radio value="pass">验证通过</el-radio>
              <el-radio value="fail">验证不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="质量指标">
            <el-input
              v-model="verifyForm.qualityMetrics"
              type="textarea"
              :rows="3"
              placeholder="请输入产品性能数据、不良率对比等质量指标"
            />
          </el-form-item>
          <el-form-item label="效果评估">
            <el-input
              v-model="verifyForm.effectAssessment"
              type="textarea"
              :rows="3"
              placeholder="请评估变更是否达到预期目的"
            />
          </el-form-item>
          <el-form-item label="改进建议">
            <el-input
              v-model="verifyForm.improvements"
              type="textarea"
              :rows="2"
              placeholder="如有需要，请提出改进建议"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showVerifyDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitVerification">提交验证</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showExecuteDialog = ref(false)
const showVerifyDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  id: '',
  title: '',
  status: '',
  executor: ''
})

// 执行表单
const executeForm = reactive({
  executeDate: '',
  executeContent: '',
  executeResult: '',
  riskPoints: ''
})

// 验证表单
const verifyForm = reactive({
  verifyDate: '',
  verifier: '',
  verifyResult: '',
  qualityMetrics: '',
  effectAssessment: '',
  improvements: ''
})

// 当前执行项目
const currentExecuteItem = ref<any>({})
const currentVerifyItem = ref<any>({})

// 示例数据
const tableData = ref([
  {
    id: 'ECN-2024-001',
    title: '发动机缸体工艺变更',
    type: 'process',
    executor: '张工程师',
    planStartDate: '2024-01-20',
    planEndDate: '2024-01-30',
    actualProgress: 75,
    executionStatus: 'executing',
    publishDate: '2024-01-15',
    ecrNumber: 'ECR-2024-001',
    projectNumber: 'PRJ-001',
    involvedAssembly: '发动机总成',
    maturity: 'B级',
    executionChecklist: [
      { department: '研发部', executionStatus: '变更发布确认（总成）是否发给客户？', plannedTime: '2024-01-20', actualTime: '2024-01-20', attachments: '发布确认单.pdf' },
      { department: '采购部', executionStatus: '是否将变更信息（图纸和变更通知单）发给供应商？', plannedTime: '2024-01-22', actualTime: '2024-01-22', attachments: '供应商通知单.pdf' },
      { department: '生产部', executionStatus: '是否完成增减或改工装模具、辅料等？', plannedTime: '2024-01-25', actualTime: '', attachments: '' },
      { department: '项目部', executionStatus: '项目执行计划是否已更新？', plannedTime: '2024-01-28', actualTime: '', attachments: '' },
      { department: '质量部', executionStatus: '是否完成新检测规范、发货检验表或制导等？', plannedTime: '2024-01-30', actualTime: '', attachments: '' },
      { department: '其他', executionStatus: '其他相关部门执行情况', plannedTime: '2024-01-30', actualTime: '', attachments: '' }
    ],
    effectVerification: [
      { sequence: 1, verificationItem: '所有部门是否完成执行内容并存档执行计划文件？', result: 'Y', remarks: '已完成' },
      { sequence: 2, verificationItem: '是否在内部和外部已经完成所有有存在的执行问题？', result: 'N', remarks: '部分问题待解决' },
      { sequence: 3, verificationItem: '变更产品总成熟点（时间/工艺/序列号等）', result: 'Y', remarks: '已确认' }
    ],
    otherRemarks: '需要关注生产线调试过程中的质量控制',
    ccbConfirmation: '待确认'
  },
  {
    id: 'ECN-2024-002',
    title: '变速箱齿轮材料变更',
    type: 'material',
    executor: '李主管',
    planStartDate: '2024-02-01',
    planEndDate: '2024-02-15',
    actualProgress: 0,
    executionStatus: 'pending',
    publishDate: '2024-01-25',
    ecrNumber: 'ECR-2024-002',
    projectNumber: 'PRJ-002',
    involvedAssembly: '变速箱总成',
    maturity: 'A级',
    executionChecklist: [
      { department: '研发部', executionStatus: '材料规格变更确认', plannedTime: '2024-02-01', actualTime: '', attachments: '' },
      { department: '采购部', executionStatus: '新供应商资质确认', plannedTime: '2024-02-03', actualTime: '', attachments: '' },
      { department: '生产部', executionStatus: '生产工艺调整', plannedTime: '2024-02-08', actualTime: '', attachments: '' },
      { department: '质量部', executionStatus: '材料检验标准更新', plannedTime: '2024-02-12', actualTime: '', attachments: '' }
    ],
    effectVerification: [
      { sequence: 1, verificationItem: '新材料是否满足性能要求？', result: '', remarks: '' },
      { sequence: 2, verificationItem: '供应商是否具备稳定供货能力？', result: '', remarks: '' }
    ],
    otherRemarks: '',
    ccbConfirmation: ''
  },
  {
    id: 'ECN-2024-003',
    title: '制动系统卡钳设计变更',
    type: 'design',
    executor: '王工程师',
    planStartDate: '2024-01-16',
    planEndDate: '2024-01-20',
    actualProgress: 100,
    executionStatus: 'verifying',
    actualEndDate: '2024-01-20',
    publishDate: '2024-01-10',
    ecrNumber: 'ECR-2024-003',
    projectNumber: 'PRJ-003',
    involvedAssembly: '制动系统',
    maturity: 'C级',
    executionChecklist: [
      { department: '研发部', executionStatus: '设计图纸更新完成', plannedTime: '2024-01-16', actualTime: '2024-01-16', attachments: '设计图纸v2.0.pdf' },
      { department: '采购部', executionStatus: '零件采购计划调整', plannedTime: '2024-01-18', actualTime: '2024-01-18', attachments: '采购计划.xlsx' },
      { department: '生产部', executionStatus: '工装夹具调整', plannedTime: '2024-01-20', actualTime: '2024-01-20', attachments: '工装调整报告.pdf' }
    ],
    effectVerification: [
      { sequence: 1, verificationItem: '新设计是否通过仿真验证？', result: 'Y', remarks: '仿真测试通过' },
      { sequence: 2, verificationItem: '制动性能是否满足要求？', result: 'Y', remarks: '性能测试合格' }
    ],
    otherRemarks: '已完成所有验证工作',
    ccbConfirmation: '已确认'
  },
  {
    id: 'ECN-2024-004',
    title: '车身焊接工艺优化',
    type: 'process',
    executor: '赵技师',
    planStartDate: '2024-01-13',
    planEndDate: '2024-01-18',
    actualProgress: 100,
    executionStatus: 'completed',
    actualEndDate: '2024-01-18',
    publishDate: '2024-01-08',
    ecrNumber: 'ECR-2024-004',
    projectNumber: 'PRJ-004',
    involvedAssembly: '车身总成',
    maturity: 'A级',
    executionChecklist: [
      { department: '研发部', executionStatus: '工艺参数优化完成', plannedTime: '2024-01-13', actualTime: '2024-01-13', attachments: '工艺参数表.pdf' },
      { department: '生产部', executionStatus: '焊接设备调试完成', plannedTime: '2024-01-15', actualTime: '2024-01-15', attachments: '设备调试报告.pdf' },
      { department: '质量部', executionStatus: '焊接质量检验标准更新', plannedTime: '2024-01-18', actualTime: '2024-01-18', attachments: '检验标准v3.0.pdf' }
    ],
    effectVerification: [
      { sequence: 1, verificationItem: '焊接强度是否达到设计要求？', result: 'Y', remarks: '强度测试合格' },
      { sequence: 2, verificationItem: '焊接效率是否有所提升？', result: 'Y', remarks: '效率提升15%' },
      { sequence: 3, verificationItem: '焊接质量稳定性如何？', result: 'Y', remarks: '质量稳定' }
    ],
    otherRemarks: '工艺优化效果显著，建议推广应用',
    ccbConfirmation: '已确认完成'
  },
  {
    id: 'ECN-2024-005',
    title: '悬架系统减震器规格变更',
    type: 'specification',
    executor: '孙工程师',
    planStartDate: '2024-01-25',
    planEndDate: '2024-02-05',
    actualProgress: 30,
    executionStatus: 'delayed',
    publishDate: '2024-01-20',
    ecrNumber: 'ECR-2024-005',
    projectNumber: 'PRJ-005',
    involvedAssembly: '悬架系统',
    maturity: 'B级',
    executionChecklist: [
      { department: '研发部', executionStatus: '减震器规格确认', plannedTime: '2024-01-25', actualTime: '2024-01-28', attachments: '规格确认单.pdf' },
      { department: '采购部', executionStatus: '供应商变更通知', plannedTime: '2024-01-30', actualTime: '', attachments: '' },
      { department: '生产部', executionStatus: '装配工艺调整', plannedTime: '2024-02-03', actualTime: '', attachments: '' },
      { department: '质量部', executionStatus: '性能测试标准更新', plannedTime: '2024-02-05', actualTime: '', attachments: '' }
    ],
    effectVerification: [
      { sequence: 1, verificationItem: '新减震器性能是否满足要求？', result: '', remarks: '' },
      { sequence: 2, verificationItem: '装配工艺是否需要调整？', result: '', remarks: '' }
    ],
    otherRemarks: '项目进度延期，需要加快执行',
    ccbConfirmation: ''
  }
])

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    process: '工艺变更',
    equipment: '设备变更',
    material: '原材料变更',
    inspection: '检验方法变更'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    process: 'primary',
    equipment: 'success',
    material: 'warning',
    inspection: 'info'
  }
  return types[type] || ''
}

const getExecutionStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    executing: '执行中',
    verifying: '待验证',
    completed: '已完成',
    delayed: '已延期'
  }
  return labels[status] || status
}

const getExecutionStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    executing: 'primary',
    verifying: 'warning',
    completed: 'success',
    delayed: 'danger'
  }
  return types[status] || ''
}

const getProgressStatus = (progress: number) => {
  if (progress === 100) return 'success'
  if (progress >= 80) return 'warning'
  return undefined
}

const getStepStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    executing: '执行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStepStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    executing: 'primary',
    completed: 'success'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    id: '',
    title: '',
    status: '',
    executor: ''
  })
}

const handleView = (row: any) => {
  console.log('查看详情', row)
  ElMessage.info('查看详情功能待实现')
}

const handleExecute = (row: any) => {
  currentExecuteItem.value = row
  showExecuteDialog.value = true
}

const handleVerify = (row: any) => {
  currentVerifyItem.value = row
  showVerifyDialog.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleExecuteDialogClose = () => {
  Object.assign(executeForm, {
    executeDate: '',
    executeContent: '',
    executeResult: '',
    riskPoints: ''
  })
}

const handleCompleteStep = (step: any) => {
  ElMessageBox.confirm('确定要完成这个执行步骤吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    step.status = 'completed'
    step.actualDate = new Date().toISOString().split('T')[0]
    ElMessage.success('步骤完成')
  })
}

const handleSaveExecuteRecord = () => {
  ElMessage.success('执行记录保存成功')
}

const handleCompleteExecution = () => {
  ElMessage.success('执行完成，进入验证阶段')
  showExecuteDialog.value = false
}

const handleSubmitVerification = () => {
  if (!verifyForm.verifyResult) {
    ElMessage.warning('请选择验证结果')
    return
  }
  
  ElMessage.success('验证提交成功')
  showVerifyDialog.value = false
  Object.assign(verifyForm, {
    verifyDate: '',
    verifier: '',
    verifyResult: '',
    qualityMetrics: '',
    effectAssessment: '',
    improvements: ''
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.change-execution {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.execute-content h3,
.verify-content h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
