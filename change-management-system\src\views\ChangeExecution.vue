<template>
  <div class="change-execution">
    <div class="page-header">
      <h2>变更执行</h2>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更编号">
          <el-input v-model="searchForm.id" placeholder="请输入变更编号" clearable />
        </el-form-item>
        <el-form-item label="变更标题">
          <el-input v-model="searchForm.title" placeholder="请输入变更标题" clearable />
        </el-form-item>
        <el-form-item label="执行状态">
          <el-select v-model="searchForm.status" placeholder="请选择执行状态" clearable>
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="executing" />
            <el-option label="待验证" value="verifying" />
            <el-option label="已完成" value="completed" />
            <el-option label="已延期" value="delayed" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任人">
          <el-input v-model="searchForm.executor" placeholder="请输入责任人" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="变更编号" width="120" />
        <el-table-column prop="title" label="变更标题" min-width="200" />
        <el-table-column prop="type" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executor" label="责任人" width="100" />
        <el-table-column prop="planStartDate" label="计划开始" width="120" />
        <el-table-column prop="planEndDate" label="计划完成" width="120" />
        <el-table-column prop="actualProgress" label="执行进度" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.actualProgress" :status="getProgressStatus(scope.row.actualProgress)" />
          </template>
        </el-table-column>
        <el-table-column prop="executionStatus" label="执行状态" width="100">
          <template #default="scope">
            <el-tag :type="getExecutionStatusTagType(scope.row.executionStatus)">
              {{ getExecutionStatusLabel(scope.row.executionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleExecute(scope.row)"
                v-if="scope.row.executionStatus === 'pending' || scope.row.executionStatus === 'executing'"
              >
                执行管理
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="handleVerify(scope.row)"
                v-if="scope.row.executionStatus === 'verifying'"
              >
                效果验证
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 执行管理对话框 -->
    <el-dialog
      v-model="showExecuteDialog"
      title="执行管理"
      width="900px"
      @close="handleExecuteDialogClose"
    >
      <div class="execute-content">
        <h3>变更信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更编号">{{ currentExecuteItem.id }}</el-descriptions-item>
          <el-descriptions-item label="变更标题">{{ currentExecuteItem.title }}</el-descriptions-item>
          <el-descriptions-item label="变更类型">{{ getTypeLabel(currentExecuteItem.type) }}</el-descriptions-item>
          <el-descriptions-item label="责任人">{{ currentExecuteItem.executor }}</el-descriptions-item>
          <el-descriptions-item label="计划开始">{{ currentExecuteItem.planStartDate }}</el-descriptions-item>
          <el-descriptions-item label="计划完成">{{ currentExecuteItem.planEndDate }}</el-descriptions-item>
        </el-descriptions>

        <h3>执行计划</h3>
        <el-table :data="currentExecuteItem.executionPlan" style="width: 100%">
          <el-table-column prop="step" label="执行步骤" width="80" />
          <el-table-column prop="description" label="步骤描述" min-width="200" />
          <el-table-column prop="planDate" label="计划日期" width="120" />
          <el-table-column prop="actualDate" label="实际日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStepStatusTagType(scope.row.status)">
                {{ getStepStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                size="small" 
                type="success" 
                @click="handleCompleteStep(scope.row)"
                v-if="scope.row.status === 'pending' || scope.row.status === 'executing'"
              >
                完成
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <h3>执行记录</h3>
        <el-form :model="executeForm" label-width="120px">
          <el-form-item label="执行日期">
            <el-date-picker
              v-model="executeForm.executeDate"
              type="date"
              placeholder="选择执行日期"
            />
          </el-form-item>
          <el-form-item label="执行内容">
            <el-input
              v-model="executeForm.executeContent"
              type="textarea"
              :rows="3"
              placeholder="请描述具体执行内容"
            />
          </el-form-item>
          <el-form-item label="执行结果">
            <el-input
              v-model="executeForm.executeResult"
              type="textarea"
              :rows="3"
              placeholder="请描述执行结果"
            />
          </el-form-item>
          <el-form-item label="风险点">
            <el-input
              v-model="executeForm.riskPoints"
              type="textarea"
              :rows="2"
              placeholder="请描述执行过程中的风险点"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExecuteDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveExecuteRecord">保存记录</el-button>
          <el-button type="success" @click="handleCompleteExecution">完成执行</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 效果验证对话框 -->
    <el-dialog
      v-model="showVerifyDialog"
      title="效果验证"
      width="700px"
    >
      <div class="verify-content">
        <h3>变更信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更编号">{{ currentVerifyItem.id }}</el-descriptions-item>
          <el-descriptions-item label="变更标题">{{ currentVerifyItem.title }}</el-descriptions-item>
          <el-descriptions-item label="责任人">{{ currentVerifyItem.executor }}</el-descriptions-item>
          <el-descriptions-item label="完成日期">{{ currentVerifyItem.actualEndDate }}</el-descriptions-item>
        </el-descriptions>

        <h3>验证表单</h3>
        <el-form :model="verifyForm" label-width="120px">
          <el-form-item label="验证日期">
            <el-date-picker
              v-model="verifyForm.verifyDate"
              type="date"
              placeholder="选择验证日期"
            />
          </el-form-item>
          <el-form-item label="验证人员">
            <el-input v-model="verifyForm.verifier" placeholder="请输入验证人员" />
          </el-form-item>
          <el-form-item label="验证结果">
            <el-radio-group v-model="verifyForm.verifyResult">
              <el-radio value="pass">验证通过</el-radio>
              <el-radio value="fail">验证不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="质量指标">
            <el-input
              v-model="verifyForm.qualityMetrics"
              type="textarea"
              :rows="3"
              placeholder="请输入产品性能数据、不良率对比等质量指标"
            />
          </el-form-item>
          <el-form-item label="效果评估">
            <el-input
              v-model="verifyForm.effectAssessment"
              type="textarea"
              :rows="3"
              placeholder="请评估变更是否达到预期目的"
            />
          </el-form-item>
          <el-form-item label="改进建议">
            <el-input
              v-model="verifyForm.improvements"
              type="textarea"
              :rows="2"
              placeholder="如有需要，请提出改进建议"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showVerifyDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitVerification">提交验证</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showExecuteDialog = ref(false)
const showVerifyDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  id: '',
  title: '',
  status: '',
  executor: ''
})

// 执行表单
const executeForm = reactive({
  executeDate: '',
  executeContent: '',
  executeResult: '',
  riskPoints: ''
})

// 验证表单
const verifyForm = reactive({
  verifyDate: '',
  verifier: '',
  verifyResult: '',
  qualityMetrics: '',
  effectAssessment: '',
  improvements: ''
})

// 当前执行项目
const currentExecuteItem = ref<any>({})
const currentVerifyItem = ref<any>({})

// 示例数据
const tableData = ref([
  {
    id: 'CHG-2024-001',
    title: '发动机装配工艺优化',
    type: 'process',
    executor: '张工程师',
    planStartDate: '2024-01-20',
    planEndDate: '2024-01-30',
    actualProgress: 75,
    executionStatus: 'executing',
    executionPlan: [
      { step: 1, description: '制定详细执行方案', planDate: '2024-01-20', actualDate: '2024-01-20', status: 'completed' },
      { step: 2, description: '采购新装配工具', planDate: '2024-01-22', actualDate: '2024-01-22', status: 'completed' },
      { step: 3, description: '员工培训', planDate: '2024-01-25', actualDate: '2024-01-25', status: 'completed' },
      { step: 4, description: '试运行测试', planDate: '2024-01-28', actualDate: '', status: 'executing' },
      { step: 5, description: '正式实施', planDate: '2024-01-30', actualDate: '', status: 'pending' }
    ]
  },
  {
    id: 'CHG-2024-002',
    title: '焊接设备升级改造',
    type: 'equipment',
    executor: '李主管',
    planStartDate: '2024-02-01',
    planEndDate: '2024-02-15',
    actualProgress: 0,
    executionStatus: 'pending',
    executionPlan: [
      { step: 1, description: '设备采购', planDate: '2024-02-01', actualDate: '', status: 'pending' },
      { step: 2, description: '设备安装', planDate: '2024-02-05', actualDate: '', status: 'pending' },
      { step: 3, description: '设备调试', planDate: '2024-02-10', actualDate: '', status: 'pending' },
      { step: 4, description: '验收测试', planDate: '2024-02-15', actualDate: '', status: 'pending' }
    ]
  },
  {
    id: 'CHG-2024-003',
    title: '钢材供应商变更',
    type: 'material',
    executor: '王采购',
    planStartDate: '2024-01-16',
    planEndDate: '2024-01-20',
    actualProgress: 100,
    executionStatus: 'verifying',
    actualEndDate: '2024-01-20',
    executionPlan: [
      { step: 1, description: '供应商资质审核', planDate: '2024-01-16', actualDate: '2024-01-16', status: 'completed' },
      { step: 2, description: '样品测试', planDate: '2024-01-18', actualDate: '2024-01-18', status: 'completed' },
      { step: 3, description: '合同签署', planDate: '2024-01-20', actualDate: '2024-01-20', status: 'completed' }
    ]
  },
  {
    id: 'CHG-2024-004',
    title: '质检标准更新',
    type: 'inspection',
    executor: '赵质检',
    planStartDate: '2024-01-13',
    planEndDate: '2024-01-18',
    actualProgress: 100,
    executionStatus: 'completed',
    actualEndDate: '2024-01-18',
    executionPlan: [
      { step: 1, description: '标准文件修订', planDate: '2024-01-13', actualDate: '2024-01-13', status: 'completed' },
      { step: 2, description: '员工培训', planDate: '2024-01-15', actualDate: '2024-01-15', status: 'completed' },
      { step: 3, description: '正式实施', planDate: '2024-01-18', actualDate: '2024-01-18', status: 'completed' }
    ]
  },
  {
    id: 'CHG-2024-006',
    title: '包装工艺改进',
    type: 'process',
    executor: '孙工程师',
    planStartDate: '2024-01-25',
    planEndDate: '2024-02-05',
    actualProgress: 30,
    executionStatus: 'delayed',
    executionPlan: [
      { step: 1, description: '包装材料采购', planDate: '2024-01-25', actualDate: '2024-01-28', status: 'completed' },
      { step: 2, description: '包装设备调整', planDate: '2024-01-30', actualDate: '', status: 'pending' },
      { step: 3, description: '试运行', planDate: '2024-02-03', actualDate: '', status: 'pending' },
      { step: 4, description: '正式实施', planDate: '2024-02-05', actualDate: '', status: 'pending' }
    ]
  }
])

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    process: '工艺变更',
    equipment: '设备变更',
    material: '原材料变更',
    inspection: '检验方法变更'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    process: 'primary',
    equipment: 'success',
    material: 'warning',
    inspection: 'info'
  }
  return types[type] || ''
}

const getExecutionStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    executing: '执行中',
    verifying: '待验证',
    completed: '已完成',
    delayed: '已延期'
  }
  return labels[status] || status
}

const getExecutionStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    executing: 'primary',
    verifying: 'warning',
    completed: 'success',
    delayed: 'danger'
  }
  return types[status] || ''
}

const getProgressStatus = (progress: number) => {
  if (progress === 100) return 'success'
  if (progress >= 80) return 'warning'
  return undefined
}

const getStepStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    executing: '执行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStepStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    executing: 'primary',
    completed: 'success'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    id: '',
    title: '',
    status: '',
    executor: ''
  })
}

const handleView = (row: any) => {
  console.log('查看详情', row)
  ElMessage.info('查看详情功能待实现')
}

const handleExecute = (row: any) => {
  currentExecuteItem.value = row
  showExecuteDialog.value = true
}

const handleVerify = (row: any) => {
  currentVerifyItem.value = row
  showVerifyDialog.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleExecuteDialogClose = () => {
  Object.assign(executeForm, {
    executeDate: '',
    executeContent: '',
    executeResult: '',
    riskPoints: ''
  })
}

const handleCompleteStep = (step: any) => {
  ElMessageBox.confirm('确定要完成这个执行步骤吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    step.status = 'completed'
    step.actualDate = new Date().toISOString().split('T')[0]
    ElMessage.success('步骤完成')
  })
}

const handleSaveExecuteRecord = () => {
  ElMessage.success('执行记录保存成功')
}

const handleCompleteExecution = () => {
  ElMessage.success('执行完成，进入验证阶段')
  showExecuteDialog.value = false
}

const handleSubmitVerification = () => {
  if (!verifyForm.verifyResult) {
    ElMessage.warning('请选择验证结果')
    return
  }
  
  ElMessage.success('验证提交成功')
  showVerifyDialog.value = false
  Object.assign(verifyForm, {
    verifyDate: '',
    verifier: '',
    verifyResult: '',
    qualityMetrics: '',
    effectAssessment: '',
    improvements: ''
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.change-execution {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.execute-content h3,
.verify-content h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
