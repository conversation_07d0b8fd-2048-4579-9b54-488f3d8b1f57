<template>
  <div class="change-review">
    <div class="page-header">
      <h2>变更审核</h2>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="变更编号">
          <el-input v-model="searchForm.id" placeholder="请输入变更编号" clearable />
        </el-form-item>
        <el-form-item label="变更标题">
          <el-input v-model="searchForm.title" placeholder="请输入变更标题" clearable />
        </el-form-item>
        <el-form-item label="审批状态">
          <el-select v-model="searchForm.status" placeholder="请选择审批状态" clearable>
            <el-option label="待我审批" value="pending_me" />
            <el-option label="审批中" value="reviewing" />
            <el-option label="已审批" value="approved" />
            <el-option label="已驳回" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="变更编号" width="120" />
        <el-table-column prop="title" label="变更标题" min-width="200" />
        <el-table-column prop="type" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="变更等级" width="100">
          <template #default="scope">
            <el-tag :type="getLevelTagType(scope.row.level)">
              {{ getLevelLabel(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100" />
        <el-table-column prop="currentApprover" label="当前审批人" width="120" />
        <el-table-column prop="submitDate" label="提交日期" width="120" />
        <el-table-column prop="reviewStatus" label="审批状态" width="120">
          <template #default="scope">
            <el-tag :type="getReviewStatusTagType(scope.row.reviewStatus)">
              {{ getReviewStatusLabel(scope.row.reviewStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="handleReview(scope.row)"
                v-if="scope.row.reviewStatus === 'pending_me'"
              >
                审批
              </el-button>
              <el-button 
                size="small" 
                type="warning" 
                @click="handleTransfer(scope.row)"
                v-if="scope.row.reviewStatus === 'pending_me'"
              >
                转派
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="showReviewDialog"
      title="变更审批"
      width="800px"
      @close="handleReviewDialogClose"
    >
      <div class="review-content">
        <h3>变更信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更编号">{{ currentReviewItem.id }}</el-descriptions-item>
          <el-descriptions-item label="变更标题">{{ currentReviewItem.title }}</el-descriptions-item>
          <el-descriptions-item label="变更类型">{{ getTypeLabel(currentReviewItem.type) }}</el-descriptions-item>
          <el-descriptions-item label="变更等级">{{ getLevelLabel(currentReviewItem.level) }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentReviewItem.applicant }}</el-descriptions-item>
          <el-descriptions-item label="提交日期">{{ currentReviewItem.submitDate }}</el-descriptions-item>
        </el-descriptions>

        <h3 style="margin-top: 20px;">变更详情</h3>
        <el-form label-width="120px">
          <el-form-item label="变更前说明">
            <div class="description-text">{{ currentReviewItem.beforeDescription }}</div>
          </el-form-item>
          <el-form-item label="变更后说明">
            <div class="description-text">{{ currentReviewItem.afterDescription }}</div>
          </el-form-item>
          <el-form-item label="影响评估">
            <div class="description-text">{{ currentReviewItem.impactAssessment }}</div>
          </el-form-item>
        </el-form>

        <h3>审批流程</h3>
        <el-steps :active="currentReviewItem.currentStep" finish-status="success">
          <el-step 
            v-for="(step, index) in currentReviewItem.approvalSteps" 
            :key="index"
            :title="step.title"
            :description="step.description"
          />
        </el-steps>

        <h3 style="margin-top: 20px;">审批意见</h3>
        <el-form :model="reviewForm" label-width="120px">
          <el-form-item label="审批结果" required>
            <el-radio-group v-model="reviewForm.result">
              <el-radio value="approve">同意</el-radio>
              <el-radio value="reject">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" required>
            <el-input
              v-model="reviewForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请填写审批意见"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showReviewDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitReview">提交审批</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 转派对话框 -->
    <el-dialog
      v-model="showTransferDialog"
      title="转派审批"
      width="500px"
    >
      <el-form :model="transferForm" label-width="100px">
        <el-form-item label="转派给" required>
          <el-select v-model="transferForm.transferTo" placeholder="请选择转派人员">
            <el-option label="张主管" value="zhang_manager" />
            <el-option label="李经理" value="li_manager" />
            <el-option label="王总监" value="wang_director" />
            <el-option label="赵副总" value="zhao_vp" />
          </el-select>
        </el-form-item>
        <el-form-item label="转派原因" required>
          <el-input
            v-model="transferForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请说明转派原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTransferDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitTransfer">确认转派</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showReviewDialog = ref(false)
const showTransferDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  id: '',
  title: '',
  status: ''
})

// 审批表单
const reviewForm = reactive({
  result: '',
  comment: ''
})

// 转派表单
const transferForm = reactive({
  transferTo: '',
  reason: ''
})

// 当前审批项目
const currentReviewItem = ref<any>({})

// 示例数据
const tableData = ref([
  {
    id: 'CHG-2024-001',
    title: '发动机装配工艺优化',
    type: 'process',
    level: 'normal',
    applicant: '张工程师',
    currentApprover: '李主管',
    submitDate: '2024-01-15',
    reviewStatus: 'pending_me',
    beforeDescription: '当前发动机装配工艺存在效率低下的问题，装配时间过长，影响生产进度。',
    afterDescription: '优化装配流程，引入新的装配工具，预计可提高装配效率30%。',
    impactAssessment: '对产品质量无负面影响，可显著提高生产效率，降低人工成本。',
    currentStep: 1,
    approvalSteps: [
      { title: '部门审批', description: '部门主管审批' },
      { title: '技术审批', description: '技术部门审批' },
      { title: '质量审批', description: '质量部门审批' },
      { title: '最终审批', description: '总经理审批' }
    ]
  },
  {
    id: 'CHG-2024-002',
    title: '焊接设备升级改造',
    type: 'equipment',
    level: 'major',
    applicant: '李主管',
    currentApprover: '王总监',
    submitDate: '2024-01-14',
    reviewStatus: 'reviewing',
    beforeDescription: '现有焊接设备老化，焊接质量不稳定，需要升级改造。',
    afterDescription: '更换新型焊接设备，提高焊接精度和稳定性。',
    impactAssessment: '需要停产改造，但长期来看可提高产品质量和生产效率。',
    currentStep: 2,
    approvalSteps: [
      { title: '部门审批', description: '部门主管审批' },
      { title: '技术审批', description: '技术部门审批' },
      { title: '质量审批', description: '质量部门审批' },
      { title: '最终审批', description: '总经理审批' }
    ]
  },
  {
    id: 'CHG-2024-003',
    title: '钢材供应商变更',
    type: 'material',
    level: 'minor',
    applicant: '王采购',
    currentApprover: '赵质检',
    submitDate: '2024-01-13',
    reviewStatus: 'pending_me',
    beforeDescription: '当前钢材供应商价格偏高，需要寻找更优质的供应商。',
    afterDescription: '更换新的钢材供应商，材料质量相同，价格更优惠。',
    impactAssessment: '可降低材料成本约15%，对产品质量无影响。',
    currentStep: 0,
    approvalSteps: [
      { title: '采购审批', description: '采购部门审批' },
      { title: '质量审批', description: '质量部门审批' }
    ]
  },
  {
    id: 'CHG-2024-004',
    title: '质检标准更新',
    type: 'inspection',
    level: 'normal',
    applicant: '赵质检',
    currentApprover: '已完成',
    submitDate: '2024-01-12',
    reviewStatus: 'approved',
    beforeDescription: '现有质检标准需要根据新的行业标准进行更新。',
    afterDescription: '更新质检标准，提高检验精度和标准化程度。',
    impactAssessment: '提高产品质量控制水平，符合最新行业要求。',
    currentStep: 3,
    approvalSteps: [
      { title: '部门审批', description: '部门主管审批' },
      { title: '技术审批', description: '技术部门审批' },
      { title: '质量审批', description: '质量部门审批' }
    ]
  },
  {
    id: 'CHG-2024-005',
    title: '生产线布局调整',
    type: 'process',
    level: 'major',
    applicant: '陈经理',
    currentApprover: '已驳回',
    submitDate: '2024-01-11',
    reviewStatus: 'rejected',
    beforeDescription: '当前生产线布局不够合理，物流路径冗长。',
    afterDescription: '重新设计生产线布局，优化物流路径。',
    impactAssessment: '需要大规模改造，成本较高，但可提高整体效率。',
    currentStep: 1,
    approvalSteps: [
      { title: '部门审批', description: '部门主管审批' },
      { title: '技术审批', description: '技术部门审批' },
      { title: '质量审批', description: '质量部门审批' },
      { title: '最终审批', description: '总经理审批' }
    ]
  }
])

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    process: '工艺变更',
    equipment: '设备变更',
    material: '原材料变更',
    inspection: '检验方法变更'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    process: 'primary',
    equipment: 'success',
    material: 'warning',
    inspection: 'info'
  }
  return types[type] || ''
}

const getLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    major: '重大变更',
    normal: '一般变更',
    minor: '轻微变更'
  }
  return labels[level] || level
}

const getLevelTagType = (level: string) => {
  const types: Record<string, string> = {
    major: 'danger',
    normal: 'warning',
    minor: 'success'
  }
  return types[level] || ''
}

const getReviewStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending_me: '待我审批',
    reviewing: '审批中',
    approved: '已审批',
    rejected: '已驳回'
  }
  return labels[status] || status
}

const getReviewStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending_me: 'warning',
    reviewing: 'primary',
    approved: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

// 事件处理函数
const handleSearch = () => {
  console.log('搜索', searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    id: '',
    title: '',
    status: ''
  })
}

const handleView = (row: any) => {
  console.log('查看详情', row)
  ElMessage.info('查看详情功能待实现')
}

const handleReview = (row: any) => {
  currentReviewItem.value = row
  showReviewDialog.value = true
}

const handleTransfer = (row: any) => {
  currentReviewItem.value = row
  showTransferDialog.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleReviewDialogClose = () => {
  Object.assign(reviewForm, {
    result: '',
    comment: ''
  })
}

const handleSubmitReview = () => {
  if (!reviewForm.result) {
    ElMessage.warning('请选择审批结果')
    return
  }
  if (!reviewForm.comment) {
    ElMessage.warning('请填写审批意见')
    return
  }
  
  const action = reviewForm.result === 'approve' ? '同意' : '驳回'
  ElMessage.success(`${action}审批成功`)
  showReviewDialog.value = false
}

const handleSubmitTransfer = () => {
  if (!transferForm.transferTo) {
    ElMessage.warning('请选择转派人员')
    return
  }
  if (!transferForm.reason) {
    ElMessage.warning('请填写转派原因')
    return
  }
  
  ElMessage.success('转派成功')
  showTransferDialog.value = false
  Object.assign(transferForm, {
    transferTo: '',
    reason: ''
  })
}

onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style scoped>
.change-review {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.review-content h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.description-text {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
