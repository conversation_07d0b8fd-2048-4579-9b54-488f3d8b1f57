# 汽车制造工厂过程变更管理系统

这是一个基于Vue 3 + Element Plus的过程变更管理系统原型图，专为汽车制造工厂设计。

## 系统功能模块

### 1. 变更管理模块
- 变更申请新增：支持录入变更基本信息、变更前后说明、影响评估等
- 审批流程选择：可根据变更类型和等级选择适用的审批流程
- 变更申请编辑与删除：支持草稿状态下的编辑和删除
- 变更状态管理：跟踪变更申请的完整生命周期
- 附件管理：支持上传相关文档和图片

### 2. 变更审核模块
- 待办提醒与通知：审批人可查看待处理的审核任务
- 在线审阅与评审：支持在线查看变更申请详细信息
- 审批操作与意见：支持同意、驳回、转派操作
- 审批流程可视化：显示当前审批进度和各环节状态
- 会签功能：支持多部门并行或串行审批

### 3. 变更执行模块
- 变更任务分配：将已批准的变更分配给责任人
- 执行计划制定与跟踪：支持制定详细执行计划并跟踪进度
- 执行过程记录：记录执行过程中的各类文档和操作日志
- 效果验证与确认：支持变更效果验证和质量确认
- 延期/风险预警：自动预警延期和风险情况

### 4. 系统配置模块
- 变更类型与等级配置：自定义变更类型和等级
- 审批流程配置：创建和管理不同的审批流程模板
- 部门配置：管理参与审批的部门信息

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **构建工具**: Vite 5.4.19
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **代码规范**: ESLint + Prettier

## 项目特点

- 🎨 **现代化UI设计**: 采用Element Plus组件库，界面美观易用
- 📱 **响应式布局**: 支持不同屏幕尺寸的自适应显示
- 🔄 **完整业务流程**: 覆盖变更管理的完整生命周期
- 📊 **数据可视化**: 提供进度条、状态标签等可视化元素
- 🛠 **高度可配置**: 支持灵活的流程和参数配置
- 💾 **示例数据**: 每个模块都配置了5条示例数据

## 快速开始

### 环境要求
- Node.js 18.x 或更高版本
- npm 或 yarn

### 安装依赖
```sh
npm install
```

### 启动开发服务器
```sh
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本
```sh
npm run build
```

## 项目结构

```
src/
├── views/              # 页面组件
│   ├── ChangeManagement.vue    # 变更管理页面
│   ├── ChangeReview.vue        # 变更审核页面
│   ├── ChangeExecution.vue     # 变更执行页面
│   └── SystemConfig.vue        # 系统配置页面
├── router/             # 路由配置
├── stores/             # 状态管理
├── components/         # 公共组件
├── assets/             # 静态资源
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 功能说明

### 操作按钮自适应
- 操作列的按钮采用flex布局，支持自适应换行
- 根据不同状态显示不同的操作按钮
- 按钮间距合理，避免挤压

### 示例数据
每个页面都配置了5条示例数据，包括：
- 不同类型的变更申请
- 不同状态的审批流程
- 不同进度的执行任务
- 完整的配置数据

### 表单验证
- 所有表单都配置了必要的验证规则
- 支持实时验证和提交验证
- 提供友好的错误提示

## 注意事项

- 这是一个原型图项目，仅用于界面展示
- 没有后台API接口，所有数据都是前端模拟
- 适用于需求演示和界面设计参考
