<template>
  <div class="system-config">
    <div class="page-header">
      <h2>系统配置</h2>
    </div>

    <el-tabs v-model="activeTab" type="card">
      <!-- 变更类型配置 -->
      <el-tab-pane label="变更类型配置" name="changeTypes">
        <el-card>
          <div class="section-header">
            <h3>变更类型管理</h3>
            <el-button type="primary" @click="showAddTypeDialog = true">
              <el-icon><Plus /></el-icon>
              新增类型
            </el-button>
          </div>
          
          <el-table :data="changeTypes" style="width: 100%">
            <el-table-column prop="code" label="类型代码" width="120" />
            <el-table-column prop="name" label="类型名称" width="150" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建日期" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" @click="handleEditType(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="scope.row.status === 'active' ? 'danger' : 'success'"
                  @click="handleToggleTypeStatus(scope.row)"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 变更等级配置 -->
      <el-tab-pane label="变更等级配置" name="changeLevels">
        <el-card>
          <div class="section-header">
            <h3>变更等级管理</h3>
            <el-button type="primary" @click="showAddLevelDialog = true">
              <el-icon><Plus /></el-icon>
              新增等级
            </el-button>
          </div>
          
          <el-table :data="changeLevels" style="width: 100%">
            <el-table-column prop="code" label="等级代码" width="120" />
            <el-table-column prop="name" label="等级名称" width="150" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="priority" label="优先级" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建日期" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" @click="handleEditLevel(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="scope.row.status === 'active' ? 'danger' : 'success'"
                  @click="handleToggleLevelStatus(scope.row)"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 审批流程配置 -->
      <el-tab-pane label="审批流程配置" name="approvalFlows">
        <el-card>
          <div class="section-header">
            <h3>审批流程管理</h3>
            <el-button type="primary" @click="showAddFlowDialog = true">
              <el-icon><Plus /></el-icon>
              新增流程
            </el-button>
          </div>
          
          <el-table :data="approvalFlows" style="width: 100%">
            <el-table-column prop="code" label="流程代码" width="120" />
            <el-table-column prop="name" label="流程名称" width="150" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="stepCount" label="审批节点数" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建日期" width="120" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="handleViewFlow(scope.row)">查看节点</el-button>
                <el-button size="small" @click="handleEditFlow(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="scope.row.status === 'active' ? 'danger' : 'success'"
                  @click="handleToggleFlowStatus(scope.row)"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 部门配置 -->
      <el-tab-pane label="部门配置" name="departments">
        <el-card>
          <div class="section-header">
            <h3>部门管理</h3>
            <el-button type="primary" @click="showAddDeptDialog = true">
              <el-icon><Plus /></el-icon>
              新增部门
            </el-button>
          </div>
          
          <el-table :data="departments" style="width: 100%">
            <el-table-column prop="code" label="部门代码" width="120" />
            <el-table-column prop="name" label="部门名称" width="150" />
            <el-table-column prop="manager" label="部门负责人" width="120" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建日期" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" @click="handleEditDept(scope.row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="scope.row.status === 'active' ? 'danger' : 'success'"
                  @click="handleToggleDeptStatus(scope.row)"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑变更类型对话框 -->
    <el-dialog
      v-model="showAddTypeDialog"
      :title="isEditType ? '编辑变更类型' : '新增变更类型'"
      width="500px"
    >
      <el-form :model="typeForm" :rules="typeRules" ref="typeFormRef" label-width="100px">
        <el-form-item label="类型代码" prop="code">
          <el-input v-model="typeForm.code" placeholder="请输入类型代码" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="typeForm.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="typeForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="typeForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddTypeDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveType">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增/编辑变更等级对话框 -->
    <el-dialog
      v-model="showAddLevelDialog"
      :title="isEditLevel ? '编辑变更等级' : '新增变更等级'"
      width="500px"
    >
      <el-form :model="levelForm" :rules="levelRules" ref="levelFormRef" label-width="100px">
        <el-form-item label="等级代码" prop="code">
          <el-input v-model="levelForm.code" placeholder="请输入等级代码" />
        </el-form-item>
        <el-form-item label="等级名称" prop="name">
          <el-input v-model="levelForm.name" placeholder="请输入等级名称" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="levelForm.priority" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="levelForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="levelForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddLevelDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveLevel">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增/编辑审批流程对话框 -->
    <el-dialog
      v-model="showAddFlowDialog"
      :title="isEditFlow ? '编辑审批流程' : '新增审批流程'"
      width="600px"
    >
      <el-form :model="flowForm" :rules="flowRules" ref="flowFormRef" label-width="100px">
        <el-form-item label="流程代码" prop="code">
          <el-input v-model="flowForm.code" placeholder="请输入流程代码" />
        </el-form-item>
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="flowForm.name" placeholder="请输入流程名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="flowForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="flowForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddFlowDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveFlow">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增/编辑部门对话框 -->
    <el-dialog
      v-model="showAddDeptDialog"
      :title="isEditDept ? '编辑部门' : '新增部门'"
      width="500px"
    >
      <el-form :model="deptForm" :rules="deptRules" ref="deptFormRef" label-width="100px">
        <el-form-item label="部门代码" prop="code">
          <el-input v-model="deptForm.code" placeholder="请输入部门代码" />
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="deptForm.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门负责人" prop="manager">
          <el-input v-model="deptForm.manager" placeholder="请输入部门负责人" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="deptForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="deptForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDeptDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDept">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('changeTypes')
const showAddTypeDialog = ref(false)
const showAddLevelDialog = ref(false)
const showAddFlowDialog = ref(false)
const showAddDeptDialog = ref(false)
const isEditType = ref(false)
const isEditLevel = ref(false)
const isEditFlow = ref(false)
const isEditDept = ref(false)

const typeFormRef = ref()
const levelFormRef = ref()
const flowFormRef = ref()
const deptFormRef = ref()

// 表单数据
const typeForm = reactive({
  code: '',
  name: '',
  description: '',
  status: 'active'
})

const levelForm = reactive({
  code: '',
  name: '',
  priority: 1,
  description: '',
  status: 'active'
})

const flowForm = reactive({
  code: '',
  name: '',
  description: '',
  status: 'active'
})

const deptForm = reactive({
  code: '',
  name: '',
  manager: '',
  description: '',
  status: 'active'
})

// 表单验证规则
const typeRules = {
  code: [{ required: true, message: '请输入类型代码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

const levelRules = {
  code: [{ required: true, message: '请输入等级代码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入等级名称', trigger: 'blur' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

const flowRules = {
  code: [{ required: true, message: '请输入流程代码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

const deptRules = {
  code: [{ required: true, message: '请输入部门代码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  manager: [{ required: true, message: '请输入部门负责人', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

// 示例数据
const changeTypes = ref([
  { code: 'PROCESS', name: '工艺变更', description: '生产工艺流程的变更', status: 'active', createDate: '2024-01-01' },
  { code: 'EQUIPMENT', name: '设备变更', description: '生产设备的变更或升级', status: 'active', createDate: '2024-01-01' },
  { code: 'MATERIAL', name: '原材料变更', description: '原材料供应商或规格的变更', status: 'active', createDate: '2024-01-01' },
  { code: 'INSPECTION', name: '检验方法变更', description: '质量检验方法或标准的变更', status: 'active', createDate: '2024-01-01' },
  { code: 'SAFETY', name: '安全变更', description: '安全规程或措施的变更', status: 'inactive', createDate: '2024-01-01' }
])

const changeLevels = ref([
  { code: 'MAJOR', name: '重大变更', description: '对生产有重大影响的变更', priority: 1, status: 'active', createDate: '2024-01-01' },
  { code: 'NORMAL', name: '一般变更', description: '对生产有一般影响的变更', priority: 2, status: 'active', createDate: '2024-01-01' },
  { code: 'MINOR', name: '轻微变更', description: '对生产影响较小的变更', priority: 3, status: 'active', createDate: '2024-01-01' },
  { code: 'URGENT', name: '紧急变更', description: '需要紧急处理的变更', priority: 0, status: 'active', createDate: '2024-01-01' },
  { code: 'ROUTINE', name: '常规变更', description: '日常例行的变更', priority: 4, status: 'inactive', createDate: '2024-01-01' }
])

const approvalFlows = ref([
  { code: 'MINOR_FLOW', name: '轻微变更流程', description: '轻微变更的审批流程', stepCount: 2, status: 'active', createDate: '2024-01-01' },
  { code: 'NORMAL_FLOW', name: '一般变更流程', description: '一般变更的审批流程', stepCount: 3, status: 'active', createDate: '2024-01-01' },
  { code: 'MAJOR_FLOW', name: '重大变更流程', description: '重大变更的审批流程', stepCount: 4, status: 'active', createDate: '2024-01-01' },
  { code: 'URGENT_FLOW', name: '紧急变更流程', description: '紧急变更的快速审批流程', stepCount: 2, status: 'active', createDate: '2024-01-01' },
  { code: 'SPECIAL_FLOW', name: '特殊变更流程', description: '特殊情况下的变更流程', stepCount: 5, status: 'inactive', createDate: '2024-01-01' }
])

const departments = ref([
  { code: 'PROD', name: '生产部', manager: '张主管', description: '负责生产制造相关工作', status: 'active', createDate: '2024-01-01' },
  { code: 'QC', name: '质量部', manager: '李经理', description: '负责质量控制和检验工作', status: 'active', createDate: '2024-01-01' },
  { code: 'TECH', name: '技术部', manager: '王总监', description: '负责技术研发和工艺改进', status: 'active', createDate: '2024-01-01' },
  { code: 'PURCHASE', name: '采购部', manager: '赵经理', description: '负责原材料和设备采购', status: 'active', createDate: '2024-01-01' },
  { code: 'SAFETY', name: '安全部', manager: '孙主管', description: '负责安全管理和监督', status: 'inactive', createDate: '2024-01-01' }
])

// 事件处理函数
const handleEditType = (row: any) => {
  isEditType.value = true
  showAddTypeDialog.value = true
  Object.assign(typeForm, row)
}

const handleToggleTypeStatus = (row: any) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}这个变更类型吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`${action}成功`)
  })
}

const handleSaveType = () => {
  typeFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      showAddTypeDialog.value = false
      isEditType.value = false
      Object.assign(typeForm, {
        code: '',
        name: '',
        description: '',
        status: 'active'
      })
    }
  })
}

const handleEditLevel = (row: any) => {
  isEditLevel.value = true
  showAddLevelDialog.value = true
  Object.assign(levelForm, row)
}

const handleToggleLevelStatus = (row: any) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}这个变更等级吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`${action}成功`)
  })
}

const handleSaveLevel = () => {
  levelFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      showAddLevelDialog.value = false
      isEditLevel.value = false
      Object.assign(levelForm, {
        code: '',
        name: '',
        priority: 1,
        description: '',
        status: 'active'
      })
    }
  })
}

const handleViewFlow = (row: any) => {
  ElMessage.info('查看审批节点功能待实现')
}

const handleEditFlow = (row: any) => {
  isEditFlow.value = true
  showAddFlowDialog.value = true
  Object.assign(flowForm, row)
}

const handleToggleFlowStatus = (row: any) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}这个审批流程吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`${action}成功`)
  })
}

const handleSaveFlow = () => {
  flowFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      showAddFlowDialog.value = false
      isEditFlow.value = false
      Object.assign(flowForm, {
        code: '',
        name: '',
        description: '',
        status: 'active'
      })
    }
  })
}

const handleEditDept = (row: any) => {
  isEditDept.value = true
  showAddDeptDialog.value = true
  Object.assign(deptForm, row)
}

const handleToggleDeptStatus = (row: any) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}这个部门吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`${action}成功`)
  })
}

const handleSaveDept = () => {
  deptFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      showAddDeptDialog.value = false
      isEditDept.value = false
      Object.assign(deptForm, {
        code: '',
        name: '',
        manager: '',
        description: '',
        status: 'active'
      })
    }
  })
}
</script>

<style scoped>
.system-config {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
